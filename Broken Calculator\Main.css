* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.calculator {
    background: rgb(9, 4, 30);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.42);
}

.display {
    width: 100%;
    height: 50px;
    margin-bottom: 10px;
    text-align: right;
    padding: 5px;
    font-size: 1.2rem;
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 60px);
    gap: 10px;
}

button {
    padding: 15px;
    font-size: 1rem;
    cursor: pointer;
    border-radius: 5px;
    border: none;
    background: #e1e0e0;
}

button:hover {
    background: #ccc;
}