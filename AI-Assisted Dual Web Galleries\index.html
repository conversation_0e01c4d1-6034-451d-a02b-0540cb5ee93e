<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dual Web Galleries</title>
    <link rel="stylesheet" href="main.css">
</head>

<body>

    <section class="carousel-container" id="carousel-container">
        <div class="carousel">
            <div class="carousel-images">
                <img src="./Carousel/dog.jpg" alt="image 1">
                <img src="./Carousel/leaf3.jpg" alt="image 2">
                <img src="./Carousel/leaf4.jpg" alt="image 3">
                <img src="./Carousel/leaf2.jpg" alt="image 4">
                <img src="./Carousel/forest.jpg" alt="image 5">
                <img src="./Carousel/leaf1.jpg" alt="image 6">
            </div>
            <button id="previous">
                < </button>
                    <button id="next"> > </button>
        </div>
    </section>

    <section class="gallery" id="gallery">
        <div class="grid">
            <div class="container-grid-images">
                <div class="image img-1">
                    <img src="./Grid/Mug.jpg" alt="image 1">
                </div>

                <div class="image img-2">
                    <img src="./Grid/Book.jpg" alt="image 2">
                </div>

                <div class="image img-3">
                    <img src="./Grid/Leaf.jpg" alt="image 3">
                </div>

                <div class="image img-4">
                    <img src="./Grid/Bench.jpg" alt="image 4">
                </div>

                <div class="image img-5">
                    <img src="./Grid/Ghost.jpg" alt="image 5">
                </div>

                <div class="image img-6">
                    <img src="./Grid/Book2.jpg" alt="image 6">
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>

</html>