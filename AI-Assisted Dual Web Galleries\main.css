* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.carousel-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: transparent;
    color: rgb(255, 255, 255);
    font-size: 2rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
}

#previous {
    left: 10px;
}

#next {
    right: 10px;
}

.carousel {
    width: 600px;
    position: relative;
}

.carousel-images {
    display: flex;
    transition: all ease-in-out 0.7s;
}

.carousel-images img {
    width: 600px;
    height: 400px;
    object-fit: cover;
}

.gallery {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    min-height: fit-content;
}

.img-1 {
    grid-area: i1;
}

.img-2 {
    grid-area: i2;
}

.img-3 {
    grid-area: i3;
}

.img-4 {
    grid-area: i4;
}

.img-5 {
    grid-area: i5;
}

.img-6 {
    grid-area: i6;
}